{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "yes": "Yes", "no": "No", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "view": "View", "print": "Print", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "actions": "Actions", "status": "Status", "noResults": "No results found", "tryDifferentSearch": "Try a different search term", "all": "All", "active": "Active", "inactive": "Inactive", "type": "Type"}, "navigation": {"dashboard": "Dashboard", "timetables": "Timetables", "teachers": "Teachers", "classes": "Classes", "subjects": "Subjects", "rooms": "Rooms", "substitutions": "Substitutions", "reports": "Reports", "settings": "Settings", "profile": "Profile", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "firstName": "First Name", "lastName": "Last Name", "role": "Role", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "Login error", "logoutSuccess": "Logout successful", "invalidCredentials": "Invalid credentials"}, "dashboard": {"welcome": "Welcome", "overview": "Overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "todaySchedule": "Today's Schedule", "upcomingEvents": "Upcoming Events"}, "timetable": {"title": "Timetable", "create": "Create Timetable", "edit": "Edit Timetable", "view": "View Timetable", "class": "Class", "subject": "Subject", "teacher": "Teacher", "room": "Room", "period": "Period", "day": "Day", "time": "Time", "duration": "Duration", "generate": "Generate", "publish": "Publish", "conflicts": "Conflicts", "noConflicts": "No Conflicts", "autoSchedule": "Auto Schedule", "manualEdit": "Manual Edit"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "teachers": {"title": "Teachers", "add": "Add Teacher", "edit": "Edit Teacher", "employeeId": "Employee ID", "name": "Name", "subjects": "Subjects", "classes": "Classes", "qualifications": "Qualifications", "experience": "Experience", "workingHours": "Working Hours", "availability": "Availability", "classTeacher": "Class Teacher", "active": "Active", "inactive": "Inactive", "noTeachers": "No teachers found", "addFirstTeacher": "Add your first teacher to get started"}, "classes": {"title": "Classes", "add": "Add Class", "edit": "Edit Class", "name": "Name", "grade": "Grade", "section": "Section", "capacity": "Capacity", "students": "Students", "classTeacher": "Class Teacher", "room": "Room", "academicYear": "Academic Year", "currentStrength": "Current Strength", "noClasses": "No classes found", "addFirstClass": "Add your first class to get started"}, "subjects": {"title": "Subjects", "add": "Add Subject", "edit": "Edit Subject", "name": "Name", "code": "Code", "department": "Department", "grade": "Grade", "periodsPerWeek": "Periods Per Week", "duration": "Duration", "core": "Core", "optional": "Optional", "prerequisites": "Prerequisites", "resources": "Resources", "noSubjects": "No subjects found", "addFirstSubject": "Add your first subject to get started"}, "rooms": {"title": "Rooms", "add": "Add Room", "edit": "Edit Room", "number": "Number", "name": "Name", "type": "Type", "building": "Building", "floor": "Floor", "capacity": "Capacity", "facilities": "Facilities", "availability": "Availability", "assignedClass": "Assigned Class", "noRooms": "No rooms found", "addFirstRoom": "Add your first room to get started"}, "substitutions": {"title": "Substitutions", "add": "Add Substitution", "edit": "Edit Substitution", "originalTeacher": "Original Teacher", "substituteTeacher": "Substitute Teacher", "date": "Date", "period": "Period", "subject": "Subject", "class": "Class", "room": "Room", "reason": "Reason", "status": "Status", "instructions": "Instructions", "feedback": "<PERSON><PERSON><PERSON>", "pending": "Pending", "confirmed": "Confirmed", "completed": "Completed", "declined": "Declined", "noSubstitutions": "No substitutions found", "addFirstSubstitution": "Add your first substitution to get started"}, "settings": {"title": "Settings", "general": "General", "language": "Language", "theme": "Theme", "notifications": "Notifications", "backup": "Backup", "users": "Users", "permissions": "Permissions", "schoolInfo": "School Information", "academicYear": "Academic Year", "terms": "Terms"}, "messages": {"saveSuccess": "Saved successfully", "saveError": "Save error", "deleteSuccess": "Deleted successfully", "deleteError": "Delete error", "updateSuccess": "Updated successfully", "updateError": "Update error", "createSuccess": "Created successfully", "createError": "Create error", "loadError": "Load error", "networkError": "Network error", "validationError": "Validation error", "permissionDenied": "Permission denied", "notFound": "Not found", "confirmDelete": "Are you sure you want to delete this?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?"}, "reports": {"title": "Reports", "generate": "Generate Report", "timetableReport": "Timetable Report", "teacherReport": "Teacher Report", "classReport": "Class Report", "substitutionReport": "Substitution Report", "attendanceReport": "Attendance Report", "exportPdf": "Export as PDF", "exportExcel": "Export as Excel"}}