import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Divider,
  FormControlLabel,
  Switch,
  Autocomplete,
  Alert,
  IconButton
} from '@mui/material'
import { Add, Remove } from '@mui/icons-material'
import axios from 'axios'

const ClassForm = ({ open, onClose, classItem, mode, onSuccess }) => {
  const { t, i18n } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [teachers, setTeachers] = useState([])
  const [subjects, setSubjects] = useState([])
  const [rooms, setRooms] = useState([])

  const [formData, setFormData] = useState({
    name: '',
    nameSinhala: '',
    grade: 1,
    section: '',
    academicYear: new Date().getFullYear(),
    capacity: 35,
    currentStrength: 0,
    classTeacher: '',
    room: '',
    subjects: [],
    schedule: {
      startTime: '07:30',
      endTime: '13:30',
      breakTimes: [
        { name: 'Short Break', startTime: '09:30', endTime: '09:45' },
        { name: 'Lunch Break', startTime: '12:00', endTime: '12:30' }
      ]
    },
    isActive: true
  })

  useEffect(() => {
    if (open) {
      fetchData()
      if (classItem && mode !== 'add') {
        setFormData({
          name: classItem.name || '',
          nameSinhala: classItem.nameSinhala || '',
          grade: classItem.grade || 1,
          section: classItem.section || '',
          academicYear: classItem.academicYear || new Date().getFullYear(),
          capacity: classItem.capacity || 35,
          currentStrength: classItem.currentStrength || 0,
          classTeacher: classItem.classTeacher?._id || '',
          room: classItem.room?._id || '',
          subjects: classItem.subjects || [],
          schedule: classItem.schedule || {
            startTime: '07:30',
            endTime: '13:30',
            breakTimes: [
              { name: 'Short Break', startTime: '09:30', endTime: '09:45' },
              { name: 'Lunch Break', startTime: '12:00', endTime: '12:30' }
            ]
          },
          isActive: classItem.isActive !== undefined ? classItem.isActive : true
        })
      } else {
        // Reset form for add mode
        setFormData({
          name: '',
          nameSinhala: '',
          grade: 1,
          section: '',
          academicYear: new Date().getFullYear(),
          capacity: 35,
          currentStrength: 0,
          classTeacher: '',
          room: '',
          subjects: [],
          schedule: {
            startTime: '07:30',
            endTime: '13:30',
            breakTimes: [
              { name: 'Short Break', startTime: '09:30', endTime: '09:45' },
              { name: 'Lunch Break', startTime: '12:00', endTime: '12:30' }
            ]
          },
          isActive: true
        })
      }
    }
  }, [open, classItem, mode])

  const fetchData = async () => {
    try {
      const [teachersRes, subjectsRes, roomsRes] = await Promise.all([
        axios.get('/api/teachers'),
        axios.get('/api/subjects'),
        axios.get('/api/rooms')
      ])
      setTeachers(teachersRes.data.teachers || [])
      setSubjects(subjectsRes.data.subjects || [])
      setRooms(roomsRes.data.rooms || [])
    } catch (err) {
      console.error('Failed to fetch data:', err)
      setError('Failed to load form data')
    }
  }

  const handleChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleSubjectChange = (index, field, value) => {
    const newSubjects = [...formData.subjects]
    newSubjects[index] = { ...newSubjects[index], [field]: value }
    setFormData(prev => ({ ...prev, subjects: newSubjects }))
  }

  const addSubject = () => {
    setFormData(prev => ({
      ...prev,
      subjects: [...prev.subjects, { subject: '', teacher: '', periodsPerWeek: 1 }]
    }))
  }

  const removeSubject = (index) => {
    const newSubjects = formData.subjects.filter((_, i) => i !== index)
    setFormData(prev => ({ ...prev, subjects: newSubjects }))
  }

  const handleBreakTimeChange = (index, field, value) => {
    const newBreakTimes = [...formData.schedule.breakTimes]
    newBreakTimes[index] = { ...newBreakTimes[index], [field]: value }
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        breakTimes: newBreakTimes
      }
    }))
  }

  const addBreakTime = () => {
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        breakTimes: [...prev.schedule.breakTimes, { name: '', startTime: '', endTime: '' }]
      }
    }))
  }

  const removeBreakTime = (index) => {
    const newBreakTimes = formData.schedule.breakTimes.filter((_, i) => i !== index)
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        breakTimes: newBreakTimes
      }
    }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError(null)

      // Validate required fields
      if (!formData.name || !formData.nameSinhala || !formData.section) {
        setError('Please fill in all required fields')
        return
      }

      const url = mode === 'add' ? '/api/classes' : `/api/classes/${classItem._id}`
      const method = mode === 'add' ? 'post' : 'put'

      await axios[method](url, formData)
      
      onSuccess?.()
      onClose()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save class')
    } finally {
      setLoading(false)
    }
  }

  const getTeacherName = (teacher) => {
    if (!teacher?.user?.profile) return 'Unknown'
    const firstName = teacher.user.profile.firstName || ''
    const lastName = teacher.user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim() || teacher.user.email
  }

  const getSubjectName = (subject) => {
    return i18n.language === 'si' ? subject.nameSinhala : subject.name
  }

  const getRoomName = (room) => {
    return i18n.language === 'si' ? (room.nameSinhala || room.name) : room.name
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'add' && t('classes.add')}
        {mode === 'edit' && t('classes.edit')}
        {mode === 'view' && t('common.view')}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={`${t('classes.name')} (English)`}
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={`${t('classes.name')} (Sinhala)`}
                value={formData.nameSinhala}
                onChange={(e) => handleChange('nameSinhala', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>{t('classes.grade')}</InputLabel>
                <Select
                  value={formData.grade}
                  onChange={(e) => handleChange('grade', e.target.value)}
                  label={t('classes.grade')}
                  disabled={mode === 'view'}
                >
                  {Array.from({ length: 13 }, (_, i) => i + 1).map((grade) => (
                    <MenuItem key={grade} value={grade}>
                      Grade {grade}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                label={t('classes.section')}
                value={formData.section}
                onChange={(e) => handleChange('section', e.target.value)}
                disabled={mode === 'view'}
                placeholder="A, B, C, etc."
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label={t('classes.academicYear')}
                value={formData.academicYear}
                onChange={(e) => handleChange('academicYear', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 2020, max: 2030 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label={t('classes.capacity')}
                value={formData.capacity}
                onChange={(e) => handleChange('capacity', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 1, max: 50 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label={t('classes.currentStrength')}
                value={formData.currentStrength}
                onChange={(e) => handleChange('currentStrength', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 0, max: formData.capacity }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>{t('classes.classTeacher')}</InputLabel>
                <Select
                  value={formData.classTeacher}
                  onChange={(e) => handleChange('classTeacher', e.target.value)}
                  label={t('classes.classTeacher')}
                  disabled={mode === 'view'}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {teachers.map((teacher) => (
                    <MenuItem key={teacher._id} value={teacher._id}>
                      {getTeacherName(teacher)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('classes.room')}</InputLabel>
                <Select
                  value={formData.room}
                  onChange={(e) => handleChange('room', e.target.value)}
                  label={t('classes.room')}
                  disabled={mode === 'view'}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {rooms.map((room) => (
                    <MenuItem key={room._id} value={room._id}>
                      {getRoomName(room)} ({room.number})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Schedule */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Schedule
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="time"
                label="Start Time"
                value={formData.schedule.startTime}
                onChange={(e) => handleChange('schedule.startTime', e.target.value)}
                disabled={mode === 'view'}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="time"
                label="End Time"
                value={formData.schedule.endTime}
                onChange={(e) => handleChange('schedule.endTime', e.target.value)}
                disabled={mode === 'view'}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            {/* Break Times */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="subtitle1">
                  Break Times
                </Typography>
                {mode !== 'view' && (
                  <Button
                    startIcon={<Add />}
                    onClick={addBreakTime}
                    size="small"
                  >
                    Add Break
                  </Button>
                )}
              </Box>
              
              {formData.schedule.breakTimes.map((breakTime, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Break Name"
                        value={breakTime.name}
                        onChange={(e) => handleBreakTimeChange(index, 'name', e.target.value)}
                        disabled={mode === 'view'}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        type="time"
                        label="Start Time"
                        value={breakTime.startTime}
                        onChange={(e) => handleBreakTimeChange(index, 'startTime', e.target.value)}
                        disabled={mode === 'view'}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        type="time"
                        label="End Time"
                        value={breakTime.endTime}
                        onChange={(e) => handleBreakTimeChange(index, 'endTime', e.target.value)}
                        disabled={mode === 'view'}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                    {mode !== 'view' && (
                      <Grid item xs={12} md={2}>
                        <IconButton
                          color="error"
                          onClick={() => removeBreakTime(index)}
                        >
                          <Remove />
                        </IconButton>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              ))}
            </Grid>

            {/* Subjects */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Subjects
                </Typography>
                {mode !== 'view' && (
                  <Button
                    startIcon={<Add />}
                    onClick={addSubject}
                    size="small"
                  >
                    Add Subject
                  </Button>
                )}
              </Box>
              
              {formData.subjects.map((subjectItem, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel>Subject</InputLabel>
                        <Select
                          value={subjectItem.subject}
                          onChange={(e) => handleSubjectChange(index, 'subject', e.target.value)}
                          label="Subject"
                          disabled={mode === 'view'}
                        >
                          {subjects.map((subject) => (
                            <MenuItem key={subject._id} value={subject._id}>
                              {getSubjectName(subject)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel>Teacher</InputLabel>
                        <Select
                          value={subjectItem.teacher}
                          onChange={(e) => handleSubjectChange(index, 'teacher', e.target.value)}
                          label="Teacher"
                          disabled={mode === 'view'}
                        >
                          {teachers.map((teacher) => (
                            <MenuItem key={teacher._id} value={teacher._id}>
                              {getTeacherName(teacher)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Periods/Week"
                        value={subjectItem.periodsPerWeek}
                        onChange={(e) => handleSubjectChange(index, 'periodsPerWeek', parseInt(e.target.value))}
                        disabled={mode === 'view'}
                        inputProps={{ min: 1, max: 10 }}
                      />
                    </Grid>
                    {mode !== 'view' && (
                      <Grid item xs={12} md={1}>
                        <IconButton
                          color="error"
                          onClick={() => removeSubject(index)}
                        >
                          <Remove />
                        </IconButton>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              ))}
            </Grid>

            {/* Status */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleChange('isActive', e.target.checked)}
                    disabled={mode === 'view'}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          {t('common.cancel')}
        </Button>
        {mode !== 'view' && (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : t('common.save')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default ClassForm
