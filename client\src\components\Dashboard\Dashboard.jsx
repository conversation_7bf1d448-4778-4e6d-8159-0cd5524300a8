import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  CircularProgress,
  Alert
} from '@mui/material'
import {
  People,
  Class,
  Subject,
  Room,
  Schedule,
  SwapHoriz,
  TrendingUp,
  Warning,
  CheckCircle,
  AccessTime
} from '@mui/icons-material'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'

const Dashboard = () => {
  const { t, i18n } = useTranslation()
  const { user } = useAuth()
  const navigate = useNavigate()
  const [dashboardData, setDashboardData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchDashboardData()
  }, [user])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      let endpoint = '/dashboard/admin'
      
      if (user?.role === 'teacher') {
        endpoint = '/dashboard/teacher'
      } else if (user?.role === 'student' || user?.role === 'parent') {
        endpoint = '/dashboard/student'
      }

      const response = await axios.get(endpoint)
      setDashboardData(response.data)
      setError(null)
    } catch (err) {
      console.error('Dashboard fetch error:', err)
      setError(err.response?.data?.messageSinhala || err.response?.data?.message || 'Failed to load dashboard')
    } finally {
      setLoading(false)
    }
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    const name = user?.profile?.firstNameSinhala && i18n.language === 'si' 
      ? user.profile.firstNameSinhala 
      : user?.profile?.firstName

    if (i18n.language === 'si') {
      if (hour < 12) return `සුභ උදෑසනක්, ${name}!`
      if (hour < 17) return `සුභ දහවලක්, ${name}!`
      return `සුභ සන්ධ්‍යාවක්, ${name}!`
    } else {
      if (hour < 12) return `Good morning, ${name}!`
      if (hour < 17) return `Good afternoon, ${name}!`
      return `Good evening, ${name}!`
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    )
  }

  // Admin Dashboard
  if (user?.role === 'admin' && dashboardData?.statistics) {
    const { overview, today, recent, conflicts } = dashboardData.statistics

    return (
      <Box>
        {/* Welcome Header */}
        <Typography variant="h4" gutterBottom>
          {getGreeting()}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {i18n.language === 'si' ? 'පාසල් කාලසටහන් කළමනාකරණ පද්ධතිය' : 'School Timetable Management System'}
        </Typography>

        <Grid container spacing={3}>
          {/* Overview Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <People color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      {t('teachers.title')}
                    </Typography>
                    <Typography variant="h4">
                      {overview.teachers.active}/{overview.teachers.total}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Class color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      {t('classes.title')}
                    </Typography>
                    <Typography variant="h4">
                      {overview.classes.active}/{overview.classes.total}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Subject color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      {t('subjects.title')}
                    </Typography>
                    <Typography variant="h4">
                      {overview.subjects.active}/{overview.subjects.total}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Schedule color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      {t('timetable.title')}
                    </Typography>
                    <Typography variant="h4">
                      {overview.timetables.published}/{overview.timetables.total}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Today's Summary */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {i18n.language === 'si' ? 'අද දින සාරාංශය' : "Today's Summary"}
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={i18n.language === 'si' ? 'නොපැමිණීම්' : 'Absences'}
                      secondary={today.absences}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <SwapHoriz color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary={i18n.language === 'si' ? 'ආදේශන' : 'Substitutions'}
                      secondary={today.substitutions}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <AccessTime color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary={i18n.language === 'si' ? 'අපේක්ෂිත ආදේශන' : 'Pending Substitutions'}
                      secondary={today.pendingSubstitutions}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Conflicts */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {i18n.language === 'si' ? 'කාලසටහන් ගැටුම්' : 'Timetable Conflicts'}
                </Typography>
                {conflicts.length === 0 ? (
                  <Box display="flex" alignItems="center" color="success.main">
                    <CheckCircle sx={{ mr: 1 }} />
                    <Typography>
                      {i18n.language === 'si' ? 'ගැටුම් නැත' : 'No conflicts found'}
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {conflicts.slice(0, 3).map((conflict, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <Warning color="warning" />
                        </ListItemIcon>
                        <ListItemText
                          primary={conflict.class?.nameSinhala || conflict.class?.name}
                          secondary={`${conflict.metadata.conflicts.length} conflicts`}
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Actions */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {i18n.language === 'si' ? 'ඉක්මන් ක්‍රියා' : 'Quick Actions'}
                </Typography>
                <Box display="flex" gap={2} flexWrap="wrap">
                  <Button variant="contained" startIcon={<Schedule />}>
                    {i18n.language === 'si' ? 'නව කාලසටහනක්' : 'New Timetable'}
                  </Button>
                  <Button variant="outlined" startIcon={<People />}>
                    {i18n.language === 'si' ? 'ගුරුවරයෙකු එකතු කරන්න' : 'Add Teacher'}
                  </Button>
                  <Button variant="outlined" startIcon={<Class />}>
                    {i18n.language === 'si' ? 'පන්තියක් එකතු කරන්න' : 'Add Class'}
                  </Button>
                  <Button variant="outlined" startIcon={<SwapHoriz />}>
                    {i18n.language === 'si' ? 'ආදේශනයක්' : 'Create Substitution'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    )
  }

  // Teacher Dashboard
  if (user?.role === 'teacher' && dashboardData?.teacher) {
    const { profile, statistics } = dashboardData.teacher

    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          {getGreeting()}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {i18n.language === 'si' ? 'ගුරු උපකරණ පුවරුව' : 'Teacher Dashboard'}
        </Typography>

        <Grid container spacing={3}>
          {/* Teacher Stats */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  {t('classes.title')}
                </Typography>
                <Typography variant="h4">
                  {statistics.totalClasses}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  {t('subjects.title')}
                </Typography>
                <Typography variant="h4">
                  {statistics.totalSubjects}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  {i18n.language === 'si' ? 'අද ආදේශන' : "Today's Substitutions"}
                </Typography>
                <Typography variant="h4">
                  {statistics.todaySubstitutions}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  {i18n.language === 'si' ? 'අපේක්ෂිත' : 'Pending'}
                </Typography>
                <Typography variant="h4">
                  {statistics.pendingSubstitutions}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Today's Schedule Placeholder */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {i18n.language === 'si' ? 'අද කාලසටහන' : "Today's Schedule"}
                </Typography>
                <Typography color="text.secondary">
                  {i18n.language === 'si' ? 'කාලසටහන් දත්ත පූරණය වෙමින්...' : 'Schedule data loading...'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    )
  }

  // Default/Student Dashboard
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {getGreeting()}
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        {i18n.language === 'si' ? 'පාසල් කාලසටහන් පද්ධතිය' : 'School Timetable System'}
      </Typography>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {i18n.language === 'si' ? 'ආයුබෝවන්!' : 'Welcome!'}
          </Typography>
          <Typography>
            {i18n.language === 'si' 
              ? 'පාසල් කාලසටහන් කළමනාකරණ පද්ධතියට ආයුබෝවන්. ඔබගේ කාලසටහන් සහ වෙනත් තොරතුරු මෙහි බලන්න.'
              : 'Welcome to the School Timetable Management System. View your schedules and other information here.'
            }
          </Typography>
        </CardContent>
      </Card>
    </Box>
  )
}

export default Dashboard
