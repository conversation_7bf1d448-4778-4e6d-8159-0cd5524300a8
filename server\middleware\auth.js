const jwt = require('jsonwebtoken');
const { User } = require('../models');

// Verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        message: 'Access token required',
        messageSinhala: 'ප්‍රවේශ ටෝකනය අවශ්‍යයි'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({ 
        message: 'Invalid token - user not found',
        messageSinhala: 'වලංගු නොවන ටෝකනය - පරිශීලකයා සොයා ගත නොහැක'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({ 
        message: 'Account is deactivated',
        messageSinhala: 'ගිණුම අක්‍රිය කර ඇත'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        message: 'Token expired',
        messageSinhala: 'ටෝකනය කල් ඉකුත් වී ඇත'
      });
    }
    
    return res.status(403).json({ 
      message: 'Invalid token',
      messageSinhala: 'වලංගු නොවන ටෝකනය'
    });
  }
};

// Role-based authorization
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        message: 'Authentication required',
        messageSinhala: 'සත්‍යාපනය අවශ්‍යයි'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Insufficient permissions',
        messageSinhala: 'ප්‍රමාණවත් අවසර නැත'
      });
    }

    next();
  };
};

// Admin only
const adminOnly = [authenticateToken, authorize('admin')];

// Admin or Teacher
const adminOrTeacher = [authenticateToken, authorize('admin', 'teacher')];

// All authenticated users
const authenticated = authenticateToken;

module.exports = {
  authenticateToken,
  authorize,
  adminOnly,
  adminOrTeacher,
  authenticated
};
