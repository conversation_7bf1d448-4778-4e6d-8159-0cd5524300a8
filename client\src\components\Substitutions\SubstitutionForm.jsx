import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Autocomplete
} from '@mui/material'
import axios from 'axios'

const SubstitutionForm = ({ open, onClose, substitution, mode, onSuccess }) => {
  const { t, i18n } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [teachers, setTeachers] = useState([])
  const [classes, setClasses] = useState([])
  const [subjects, setSubjects] = useState([])

  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    period: 1,
    originalTeacher: '',
    substituteTeacher: '',
    subject: '',
    class: '',
    reason: '',
    type: 'teacher_substitution',
    status: 'pending',
    instructions: '',
    notes: ''
  })

  const typeOptions = [
    { value: 'teacher_substitution', label: 'Teacher Substitution', labelSi: 'ගුරු ආදේශනය' },
    { value: 'free_period', label: 'Free Period', labelSi: 'නිදහස් කාලය' },
    { value: 'combined_class', label: 'Combined Class', labelSi: 'ඒකාබද්ධ පන්තිය' },
    { value: 'self_study', label: 'Self Study', labelSi: 'ස්වයං අධ්‍යයනය' },
    { value: 'cancelled', label: 'Cancelled', labelSi: 'අවලංගු කළ' }
  ]

  const statusOptions = [
    { value: 'pending', label: 'Pending', labelSi: 'අපේක්ෂිත' },
    { value: 'confirmed', label: 'Confirmed', labelSi: 'තහවුරු කළ' },
    { value: 'declined', label: 'Declined', labelSi: 'ප්‍රතික්ෂේප කළ' },
    { value: 'completed', label: 'Completed', labelSi: 'සම්පූර්ණ' }
  ]

  useEffect(() => {
    if (open) {
      fetchData()
      if (substitution && mode !== 'add') {
        setFormData({
          date: substitution.date ? new Date(substitution.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          period: substitution.period || 1,
          originalTeacher: substitution.originalTeacher?._id || '',
          substituteTeacher: substitution.substituteTeacher?._id || '',
          subject: substitution.subject?._id || '',
          class: substitution.class?._id || '',
          reason: substitution.reason || '',
          type: substitution.type || 'teacher_substitution',
          status: substitution.status || 'pending',
          instructions: substitution.instructions || '',
          notes: substitution.notes || ''
        })
      } else {
        setFormData({
          date: new Date().toISOString().split('T')[0],
          period: 1,
          originalTeacher: '',
          substituteTeacher: '',
          subject: '',
          class: '',
          reason: '',
          type: 'teacher_substitution',
          status: 'pending',
          instructions: '',
          notes: ''
        })
      }
    }
  }, [open, substitution, mode])

  const fetchData = async () => {
    try {
      const [teachersRes, classesRes, subjectsRes] = await Promise.all([
        axios.get('/api/teachers'),
        axios.get('/api/classes'),
        axios.get('/api/subjects')
      ])
      setTeachers(teachersRes.data.teachers || [])
      setClasses(classesRes.data.classes || [])
      setSubjects(subjectsRes.data.subjects || [])
    } catch (err) {
      console.error('Failed to fetch data:', err)
      setError('Failed to load form data')
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!formData.date || !formData.originalTeacher || !formData.subject || !formData.class) {
        setError('Please fill in all required fields')
        return
      }

      const url = mode === 'add' ? '/api/substitutions' : `/api/substitutions/${substitution._id}`
      const method = mode === 'add' ? 'post' : 'put'

      await axios[method](url, formData)
      
      onSuccess?.()
      onClose()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save substitution')
    } finally {
      setLoading(false)
    }
  }

  const getTeacherName = (teacher) => {
    if (!teacher?.user?.profile) return 'Unknown'
    const firstName = teacher.user.profile.firstName || ''
    const lastName = teacher.user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim() || teacher.user.email
  }

  const getClassName = (classItem) => {
    return i18n.language === 'si' ? classItem.nameSinhala : classItem.name
  }

  const getSubjectName = (subject) => {
    return i18n.language === 'si' ? subject.nameSinhala : subject.name
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'add' && t('substitutions.add')}
        {mode === 'edit' && t('substitutions.edit')}
        {mode === 'view' && t('common.view')}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                type="date"
                label={t('substitutions.date')}
                value={formData.date}
                onChange={(e) => handleChange('date', e.target.value)}
                disabled={mode === 'view'}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                type="number"
                label={t('substitutions.period')}
                value={formData.period}
                onChange={(e) => handleChange('period', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 1, max: 10 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>{t('substitutions.originalTeacher')}</InputLabel>
                <Select
                  value={formData.originalTeacher}
                  onChange={(e) => handleChange('originalTeacher', e.target.value)}
                  label={t('substitutions.originalTeacher')}
                  disabled={mode === 'view'}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                        width: 300,
                      },
                    },
                  }}
                >
                  {teachers.map((teacher) => (
                    <MenuItem key={teacher._id} value={teacher._id}>
                      {getTeacherName(teacher)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('substitutions.substituteTeacher')}</InputLabel>
                <Select
                  value={formData.substituteTeacher}
                  onChange={(e) => handleChange('substituteTeacher', e.target.value)}
                  label={t('substitutions.substituteTeacher')}
                  disabled={mode === 'view'}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {teachers.map((teacher) => (
                    <MenuItem key={teacher._id} value={teacher._id}>
                      {getTeacherName(teacher)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>{t('substitutions.subject')}</InputLabel>
                <Select
                  value={formData.subject}
                  onChange={(e) => handleChange('subject', e.target.value)}
                  label={t('substitutions.subject')}
                  disabled={mode === 'view'}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                        width: 250,
                      },
                    },
                  }}
                >
                  {subjects.map((subject) => (
                    <MenuItem key={subject._id} value={subject._id}>
                      {getSubjectName(subject)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>{t('substitutions.class')}</InputLabel>
                <Select
                  value={formData.class}
                  onChange={(e) => handleChange('class', e.target.value)}
                  label={t('substitutions.class')}
                  disabled={mode === 'view'}
                >
                  {classes.map((classItem) => (
                    <MenuItem key={classItem._id} value={classItem._id}>
                      {getClassName(classItem)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => handleChange('type', e.target.value)}
                  label="Type"
                  disabled={mode === 'view'}
                >
                  {typeOptions.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {i18n.language === 'si' ? type.labelSi : type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('substitutions.status')}</InputLabel>
                <Select
                  value={formData.status}
                  onChange={(e) => handleChange('status', e.target.value)}
                  label={t('substitutions.status')}
                  disabled={mode === 'view'}
                >
                  {statusOptions.map((status) => (
                    <MenuItem key={status.value} value={status.value}>
                      {i18n.language === 'si' ? status.labelSi : status.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('substitutions.reason')}
                value={formData.reason}
                onChange={(e) => handleChange('reason', e.target.value)}
                disabled={mode === 'view'}
                placeholder="Reason for substitution..."
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label={t('substitutions.instructions')}
                value={formData.instructions}
                onChange={(e) => handleChange('instructions', e.target.value)}
                disabled={mode === 'view'}
                placeholder="Instructions for the substitute teacher..."
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Notes"
                value={formData.notes}
                onChange={(e) => handleChange('notes', e.target.value)}
                disabled={mode === 'view'}
                placeholder="Additional notes..."
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          {t('common.cancel')}
        </Button>
        {mode !== 'view' && (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : t('common.save')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default SubstitutionForm
