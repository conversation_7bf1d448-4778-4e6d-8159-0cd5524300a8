import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar,
  Grid
} from '@mui/material'
import {
  Add,
  Class,
  Edit,
  Delete,
  Search,
  Visibility,
  School,
  Person,
  Room,
  People
} from '@mui/icons-material'
import axios from 'axios'

const ClassList = () => {
  const { t, i18n } = useTranslation()
  const [classes, setClasses] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedClass, setSelectedClass] = useState(null)
  const [dialogMode, setDialogMode] = useState('add') // 'add', 'edit', 'view'

  useEffect(() => {
    fetchClasses()
  }, [page, searchTerm])

  const fetchClasses = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/classes', {
        params: {
          page,
          limit: 10,
          search: searchTerm
        }
      })
      setClasses(response.data.classes)
      setTotalPages(response.data.pagination.totalPages)
      setError(null)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch classes')
    } finally {
      setLoading(false)
    }
  }

  const handleAddClass = () => {
    setSelectedClass(null)
    setDialogMode('add')
    setOpenDialog(true)
  }

  const handleEditClass = (classItem) => {
    setSelectedClass(classItem)
    setDialogMode('edit')
    setOpenDialog(true)
  }

  const handleViewClass = (classItem) => {
    setSelectedClass(classItem)
    setDialogMode('view')
    setOpenDialog(true)
  }

  const handleDeleteClass = async (classId) => {
    if (window.confirm(t('messages.confirmDelete'))) {
      try {
        await axios.delete(`/api/classes/${classId}`)
        fetchClasses()
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete class')
      }
    }
  }

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  const handlePageChange = (event, newPage) => {
    setPage(newPage)
  }

  const getClassInitials = (classItem) => {
    return `${classItem.grade}${classItem.section}`.toUpperCase()
  }

  const getClassName = (classItem) => {
    return i18n.language === 'si' ? classItem.nameSinhala : classItem.name
  }

  const getClassTeacherName = (teacher) => {
    if (!teacher?.user?.profile) return 'Not assigned'
    const firstName = teacher.user.profile.firstName || ''
    const lastName = teacher.user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim() || 'Unknown'
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3} sx={{ gap: 2 }}>
        <Typography variant="h4">{t('classes.title')}</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={handleAddClass}>
          {t('classes.add')}
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center">
            <TextField
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Classes Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : classes.length === 0 ? (
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <Class sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {searchTerm ? t('common.noResults') : t('classes.noClasses')}
              </Typography>
              <Typography color="text.secondary">
                {searchTerm ? t('common.tryDifferentSearch') : t('classes.addFirstClass')}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('classes.name')}</TableCell>
                      <TableCell>{t('classes.grade')}</TableCell>
                      <TableCell>{t('classes.section')}</TableCell>
                      <TableCell>{t('classes.capacity')}</TableCell>
                      <TableCell>{t('classes.classTeacher')}</TableCell>
                      <TableCell>{t('classes.room')}</TableCell>
                      <TableCell align="right">{t('common.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {classes.map((classItem) => (
                      <TableRow key={classItem._id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar sx={{ bgcolor: 'secondary.main' }}>
                              {getClassInitials(classItem)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {getClassName(classItem)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {t('classes.academicYear')}: {classItem.academicYear}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`Grade ${classItem.grade}`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {classItem.section}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <People fontSize="small" color="action" />
                            <Typography variant="body2">
                              {classItem.currentStrength || 0}/{classItem.capacity}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {classItem.classTeacher ? getClassTeacherName(classItem.classTeacher) : 'Not assigned'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Room fontSize="small" color="action" />
                            <Typography variant="body2">
                              {classItem.room ?
                                (i18n.language === 'si' ? classItem.room.nameSinhala : classItem.room.name) :
                                'Not assigned'
                              }
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" gap={1}>
                            <Tooltip title={t('common.view')}>
                              <IconButton
                                size="small"
                                onClick={() => handleViewClass(classItem)}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.edit')}>
                              <IconButton
                                size="small"
                                onClick={() => handleEditClass(classItem)}
                              >
                                <Edit />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.delete')}>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteClass(classItem._id)}
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Class Dialog - Placeholder for now */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' && t('classes.add')}
          {dialogMode === 'edit' && t('classes.edit')}
          {dialogMode === 'view' && t('common.view')}
        </DialogTitle>
        <DialogContent>
          <Box py={2}>
            <Typography>
              {dialogMode === 'add' && 'Add class form will be implemented here'}
              {dialogMode === 'edit' && 'Edit class form will be implemented here'}
              {dialogMode === 'view' && 'Class details view will be implemented here'}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            {t('common.close')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ClassList
