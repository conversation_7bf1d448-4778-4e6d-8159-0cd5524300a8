import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  FormControlLabel,
  Switch,
  Alert,
  Chip,
  Autocomplete
} from '@mui/material'
import axios from 'axios'

const RoomForm = ({ open, onClose, room, mode, onSuccess }) => {
  const { t, i18n } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [classes, setClasses] = useState([])

  const [formData, setFormData] = useState({
    name: '',
    nameSinhala: '',
    number: '',
    type: 'classroom',
    building: '',
    floor: 1,
    capacity: 35,
    facilities: [],
    assignedClass: '',
    isActive: true,
    description: ''
  })

  const roomTypes = [
    { value: 'classroom', label: 'Classroom', labelSi: 'පන්ති කාමරය' },
    { value: 'laboratory', label: 'Laboratory', labelSi: 'රසායනාගාරය' },
    { value: 'computer_lab', label: 'Computer Lab', labelSi: 'පරිගණක රසායනාගාරය' },
    { value: 'library', label: 'Library', labelSi: 'පුස්තකාලය' },
    { value: 'auditorium', label: 'Auditorium', labelSi: 'ශ්‍රවණාගාරය' },
    { value: 'gymnasium', label: 'Gymnasium', labelSi: 'ව්‍යායාමාගාරය' },
    { value: 'art_room', label: 'Art Room', labelSi: 'කලා කාමරය' },
    { value: 'music_room', label: 'Music Room', labelSi: 'සංගීත කාමරය' },
    { value: 'staff_room', label: 'Staff Room', labelSi: 'කාර්ය මණ්ඩල කාමරය' },
    { value: 'office', label: 'Office', labelSi: 'කාර්යාලය' }
  ]

  const facilityOptions = [
    'projector', 'whiteboard', 'blackboard', 'computer', 'internet',
    'air_conditioning', 'fan', 'sound_system', 'microphone', 'stage',
    'laboratory_equipment', 'sports_equipment', 'musical_instruments'
  ]

  useEffect(() => {
    if (open) {
      fetchClasses()
      if (room && mode !== 'add') {
        setFormData({
          name: room.name || '',
          nameSinhala: room.nameSinhala || '',
          number: room.number || '',
          type: room.type || 'classroom',
          building: room.building || '',
          floor: room.floor || 1,
          capacity: room.capacity || 35,
          facilities: room.facilities || [],
          assignedClass: room.assignedClass?._id || '',
          isActive: room.isActive !== undefined ? room.isActive : true,
          description: room.description || ''
        })
      } else {
        setFormData({
          name: '',
          nameSinhala: '',
          number: '',
          type: 'classroom',
          building: '',
          floor: 1,
          capacity: 35,
          facilities: [],
          assignedClass: '',
          isActive: true,
          description: ''
        })
      }
    }
  }, [open, room, mode])

  const fetchClasses = async () => {
    try {
      const response = await axios.get('/api/classes')
      setClasses(response.data.classes || [])
    } catch (err) {
      console.error('Failed to fetch classes:', err)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!formData.name || !formData.nameSinhala || !formData.number || !formData.building) {
        setError('Please fill in all required fields')
        return
      }

      const url = mode === 'add' ? '/api/rooms' : `/api/rooms/${room._id}`
      const method = mode === 'add' ? 'post' : 'put'

      await axios[method](url, formData)
      
      onSuccess?.()
      onClose()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save room')
    } finally {
      setLoading(false)
    }
  }

  const getClassName = (classItem) => {
    return i18n.language === 'si' ? classItem.nameSinhala : classItem.name
  }

  const getRoomTypeLabel = (type) => {
    const roomType = roomTypes.find(t => t.value === type)
    return roomType ? (i18n.language === 'si' ? roomType.labelSi : roomType.label) : type
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'add' && t('rooms.add')}
        {mode === 'edit' && t('rooms.edit')}
        {mode === 'view' && t('common.view')}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={`${t('rooms.name')} (English)`}
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={`${t('rooms.name')} (Sinhala)`}
                value={formData.nameSinhala}
                onChange={(e) => handleChange('nameSinhala', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                label={t('rooms.number')}
                value={formData.number}
                onChange={(e) => handleChange('number', e.target.value)}
                disabled={mode === 'view'}
                placeholder="101, A-201, etc."
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>{t('rooms.type')}</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => handleChange('type', e.target.value)}
                  label={t('rooms.type')}
                  disabled={mode === 'view'}
                >
                  {roomTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {i18n.language === 'si' ? type.labelSi : type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                label={t('rooms.building')}
                value={formData.building}
                onChange={(e) => handleChange('building', e.target.value)}
                disabled={mode === 'view'}
                placeholder="Main Building, Block A, etc."
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label={t('rooms.floor')}
                value={formData.floor}
                onChange={(e) => handleChange('floor', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 0, max: 10 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label={t('rooms.capacity')}
                value={formData.capacity}
                onChange={(e) => handleChange('capacity', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 1, max: 100 }}
              />
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={facilityOptions}
                value={formData.facilities}
                onChange={(event, newValue) => handleChange('facilities', newValue)}
                disabled={mode === 'view'}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.replace('_', ' ').toUpperCase()}
                      {...getTagProps({ index })}
                      key={option}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('rooms.facilities')}
                    placeholder="Select available facilities"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('rooms.assignedClass')}</InputLabel>
                <Select
                  value={formData.assignedClass}
                  onChange={(e) => handleChange('assignedClass', e.target.value)}
                  label={t('rooms.assignedClass')}
                  disabled={mode === 'view'}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {classes.map((classItem) => (
                    <MenuItem key={classItem._id} value={classItem._id}>
                      {getClassName(classItem)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleChange('isActive', e.target.checked)}
                    disabled={mode === 'view'}
                  />
                }
                label={formData.isActive ? t('common.active') : t('common.inactive')}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                disabled={mode === 'view'}
                placeholder="Additional information about the room..."
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          {t('common.cancel')}
        </Button>
        {mode !== 'view' && (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : t('common.save')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default RoomForm
