import React, { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Box } from '@mui/material'
import { toast } from 'react-toastify'

// Components
import Layout from './components/Layout/Layout'
import Login from './components/Auth/Login'
import Dashboard from './components/Dashboard/Dashboard'
import TimetableList from './components/Timetables/TimetableList'
import TimetableView from './components/Timetables/TimetableView'
import TeacherList from './components/Teachers/TeacherList'
import ClassList from './components/Classes/ClassList'
import SubjectList from './components/Subjects/SubjectList'
import RoomList from './components/Rooms/RoomList'
import SubstitutionList from './components/Substitutions/SubstitutionList'

// Context
import { AuthProvider, useAuth } from './contexts/AuthContext'

// Protected Route Component
const ProtectedRoute = ({ children, roles = [] }) => {
  const { user, isAuthenticated } = useAuth()

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  if (roles.length > 0 && !roles.includes(user?.role)) {
    toast.error('Access denied')
    return <Navigate to="/dashboard" replace />
  }

  return children
}

// Main App Component
function App() {
  const { i18n } = useTranslation()

  useEffect(() => {
    // Set document direction and language
    document.documentElement.lang = i18n.language
    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr'
  }, [i18n.language])

  return (
    <AuthProvider>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            {/* Timetables */}
            <Route path="timetables" element={<TimetableList />} />
            <Route path="timetables/:id" element={<TimetableView />} />

            {/* Teachers */}
            <Route path="teachers" element={
              <ProtectedRoute roles={['admin', 'teacher']}>
                <TeacherList />
              </ProtectedRoute>
            } />

            {/* Classes */}
            <Route path="classes" element={
              <ProtectedRoute roles={['admin', 'teacher']}>
                <ClassList />
              </ProtectedRoute>
            } />

            {/* Subjects */}
            <Route path="subjects" element={
              <ProtectedRoute roles={['admin', 'teacher']}>
                <SubjectList />
              </ProtectedRoute>
            } />

            {/* Rooms */}
            <Route path="rooms" element={
              <ProtectedRoute roles={['admin', 'teacher']}>
                <RoomList />
              </ProtectedRoute>
            } />

            {/* Substitutions */}
            <Route path="substitutions" element={
              <ProtectedRoute roles={['admin', 'teacher']}>
                <SubstitutionList />
              </ProtectedRoute>
            } />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Box>
    </AuthProvider>
  )
}

export default App
