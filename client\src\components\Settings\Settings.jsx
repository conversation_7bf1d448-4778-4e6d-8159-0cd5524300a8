import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material'
import {
  Settings as SettingsIcon,
  School,
  Language,
  Palette,
  Notifications,
  Security,
  Backup,
  People,
  Edit,
  Delete,
  Add
} from '@mui/icons-material'
import { useAuth } from '../../contexts/AuthContext'
import axios from 'axios'

const Settings = () => {
  const { t, i18n } = useTranslation()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  
  // School Information
  const [schoolInfo, setSchoolInfo] = useState({
    name: 'School Timetable Management System',
    nameSinhala: 'පාසල් කාලසටහන් කළමනාකරණ පද්ධතිය',
    address: '',
    phone: '',
    email: '',
    website: '',
    principalName: '',
    academicYear: new Date().getFullYear(),
    terms: 3
  })

  // Language & Theme Settings
  const [preferences, setPreferences] = useState({
    language: i18n.language,
    theme: 'light',
    notifications: true,
    emailNotifications: true,
    autoBackup: true,
    backupFrequency: 'daily'
  })

  // Users Management
  const [users, setUsers] = useState([])
  const [openUserDialog, setOpenUserDialog] = useState(false)

  useEffect(() => {
    fetchSettings()
    fetchUsers()
  }, [])

  const fetchSettings = async () => {
    try {
      // This would fetch actual settings from the server
      // For now, using default values
    } catch (err) {
      setError('Failed to load settings')
    }
  }

  const fetchUsers = async () => {
    try {
      // This would fetch users from the server
      // For now, using mock data
      setUsers([
        { id: 1, name: 'Admin User', email: '<EMAIL>', role: 'admin', active: true },
        { id: 2, name: 'Teacher User', email: '<EMAIL>', role: 'teacher', active: true }
      ])
    } catch (err) {
      setError('Failed to load users')
    }
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const handleSchoolInfoChange = (field, value) => {
    setSchoolInfo(prev => ({ ...prev, [field]: value }))
  }

  const handlePreferenceChange = (field, value) => {
    setPreferences(prev => ({ ...prev, [field]: value }))
    
    // Apply language change immediately
    if (field === 'language') {
      i18n.changeLanguage(value)
    }
  }

  const handleSaveSettings = async () => {
    try {
      setLoading(true)
      // Save settings to server
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
      setSuccess('Settings saved successfully')
      setTimeout(() => setSuccess(null), 3000)
    } catch (err) {
      setError('Failed to save settings')
    } finally {
      setLoading(false)
    }
  }

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  )

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3} gap={2}>
        <SettingsIcon color="primary" sx={{ fontSize: 32 }} />
        <Typography variant="h4">{t('settings.title')}</Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab 
              label={t('settings.general')} 
              icon={<School />} 
              iconPosition="start"
            />
            <Tab 
              label={t('settings.language')} 
              icon={<Language />} 
              iconPosition="start"
            />
            <Tab 
              label={t('settings.notifications')} 
              icon={<Notifications />} 
              iconPosition="start"
            />
            <Tab 
              label={t('settings.users')} 
              icon={<People />} 
              iconPosition="start"
            />
          </Tabs>
        </Box>

        <CardContent>
          {/* General Settings */}
          <TabPanel value={activeTab} index={0}>
            <Typography variant="h6" gutterBottom>
              {t('settings.schoolInfo')}
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="School Name (English)"
                  value={schoolInfo.name}
                  onChange={(e) => handleSchoolInfoChange('name', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="School Name (Sinhala)"
                  value={schoolInfo.nameSinhala}
                  onChange={(e) => handleSchoolInfoChange('nameSinhala', e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  multiline
                  rows={2}
                  value={schoolInfo.address}
                  onChange={(e) => handleSchoolInfoChange('address', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={schoolInfo.phone}
                  onChange={(e) => handleSchoolInfoChange('phone', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={schoolInfo.email}
                  onChange={(e) => handleSchoolInfoChange('email', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Website"
                  value={schoolInfo.website}
                  onChange={(e) => handleSchoolInfoChange('website', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Principal Name"
                  value={schoolInfo.principalName}
                  onChange={(e) => handleSchoolInfoChange('principalName', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label={t('settings.academicYear')}
                  type="number"
                  value={schoolInfo.academicYear}
                  onChange={(e) => handleSchoolInfoChange('academicYear', parseInt(e.target.value))}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label={t('settings.terms')}
                  type="number"
                  value={schoolInfo.terms}
                  onChange={(e) => handleSchoolInfoChange('terms', parseInt(e.target.value))}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Language & Theme Settings */}
          <TabPanel value={activeTab} index={1}>
            <Typography variant="h6" gutterBottom>
              {t('settings.language')} & {t('settings.theme')}
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('settings.language')}</InputLabel>
                  <Select
                    value={preferences.language}
                    onChange={(e) => handlePreferenceChange('language', e.target.value)}
                    label={t('settings.language')}
                  >
                    <MenuItem value="si">සිංහල (Sinhala)</MenuItem>
                    <MenuItem value="en">English</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('settings.theme')}</InputLabel>
                  <Select
                    value={preferences.theme}
                    onChange={(e) => handlePreferenceChange('theme', e.target.value)}
                    label={t('settings.theme')}
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="auto">Auto</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Notifications */}
          <TabPanel value={activeTab} index={2}>
            <Typography variant="h6" gutterBottom>
              {t('settings.notifications')}
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="Push Notifications"
                  secondary="Receive notifications in the app"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={preferences.notifications}
                    onChange={(e) => handlePreferenceChange('notifications', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Email Notifications"
                  secondary="Receive notifications via email"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={preferences.emailNotifications}
                    onChange={(e) => handlePreferenceChange('emailNotifications', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Auto Backup"
                  secondary="Automatically backup data"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={preferences.autoBackup}
                    onChange={(e) => handlePreferenceChange('autoBackup', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </TabPanel>

          {/* Users Management */}
          <TabPanel value={activeTab} index={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                {t('settings.users')}
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setOpenUserDialog(true)}
              >
                Add User
              </Button>
            </Box>
            <List>
              {users.map((user) => (
                <ListItem key={user.id} divider>
                  <Avatar sx={{ mr: 2 }}>{user.name.charAt(0)}</Avatar>
                  <ListItemText
                    primary={user.name}
                    secondary={`${user.email} • ${user.role}`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton size="small">
                      <Edit />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <Delete />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </TabPanel>

          <Divider sx={{ my: 3 }} />
          
          <Box display="flex" justifyContent="flex-end" gap={2}>
            <Button variant="outlined">
              {t('common.cancel')}
            </Button>
            <Button 
              variant="contained" 
              onClick={handleSaveSettings}
              disabled={loading}
            >
              {loading ? 'Saving...' : t('common.save')}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* User Dialog */}
      <Dialog open={openUserDialog} onClose={() => setOpenUserDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New User</DialogTitle>
        <DialogContent>
          <Typography>User management form will be implemented here</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenUserDialog(false)}>Cancel</Button>
          <Button variant="contained">Add User</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Settings
