import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Add,
  Room,
  Edit,
  Delete,
  Search,
  Visibility,
  Business,
  Layers,
  People,
  Class
} from '@mui/icons-material'
import axios from 'axios'

const RoomList = () => {
  const { t, i18n } = useTranslation()
  const [rooms, setRooms] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [buildingFilter, setBuildingFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedRoom, setSelectedRoom] = useState(null)
  const [dialogMode, setDialogMode] = useState('add') // 'add', 'edit', 'view'

  const roomTypes = [
    { value: 'classroom', label: 'Classroom', labelSi: 'පන්ති කාමරය' },
    { value: 'laboratory', label: 'Laboratory', labelSi: 'රසායනාගාරය' },
    { value: 'computer_lab', label: 'Computer Lab', labelSi: 'පරිගණක රසායනාගාරය' },
    { value: 'library', label: 'Library', labelSi: 'පුස්තකාලය' },
    { value: 'auditorium', label: 'Auditorium', labelSi: 'ශ්‍රවණාගාරය' },
    { value: 'gymnasium', label: 'Gymnasium', labelSi: 'ව්‍යායාමාගාරය' },
    { value: 'art_room', label: 'Art Room', labelSi: 'කලා කාමරය' },
    { value: 'music_room', label: 'Music Room', labelSi: 'සංගීත කාමරය' },
    { value: 'staff_room', label: 'Staff Room', labelSi: 'කාර්ය මණ්ඩල කාමරය' },
    { value: 'office', label: 'Office', labelSi: 'කාර්යාලය' }
  ]

  useEffect(() => {
    fetchRooms()
  }, [page, searchTerm, typeFilter, buildingFilter])

  const fetchRooms = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/rooms', {
        params: {
          page,
          limit: 10,
          search: searchTerm,
          type: typeFilter,
          building: buildingFilter
        }
      })
      setRooms(response.data.rooms)
      setTotalPages(response.data.pagination.totalPages)
      setError(null)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch rooms')
    } finally {
      setLoading(false)
    }
  }

  const handleAddRoom = () => {
    setSelectedRoom(null)
    setDialogMode('add')
    setOpenDialog(true)
  }

  const handleEditRoom = (room) => {
    setSelectedRoom(room)
    setDialogMode('edit')
    setOpenDialog(true)
  }

  const handleViewRoom = (room) => {
    setSelectedRoom(room)
    setDialogMode('view')
    setOpenDialog(true)
  }

  const handleDeleteRoom = async (roomId) => {
    if (window.confirm(t('messages.confirmDelete'))) {
      try {
        await axios.delete(`/api/rooms/${roomId}`)
        fetchRooms()
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete room')
      }
    }
  }

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  const handleTypeChange = (event) => {
    setTypeFilter(event.target.value)
    setPage(1)
  }

  const handleBuildingChange = (event) => {
    setBuildingFilter(event.target.value)
    setPage(1)
  }

  const handlePageChange = (event, newPage) => {
    setPage(newPage)
  }

  const getRoomInitials = (room) => {
    return room.number || room.name.substring(0, 2).toUpperCase()
  }

  const getRoomName = (room) => {
    return i18n.language === 'si' ? (room.nameSinhala || room.name) : room.name
  }

  const getRoomTypeLabel = (type) => {
    const roomType = roomTypes.find(t => t.value === type)
    return roomType ? (i18n.language === 'si' ? roomType.labelSi : roomType.label) : type
  }

  const getAssignedClassName = (assignedClass) => {
    if (!assignedClass) return 'Not assigned'
    return i18n.language === 'si' ? assignedClass.nameSinhala : assignedClass.name
  }

  // Get unique buildings for filter
  const buildings = [...new Set(rooms.map(room => room.building).filter(Boolean))]

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3} sx={{ gap: 2 }}>
        <Typography variant="h4">{t('rooms.title')}</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={handleAddRoom}>
          {t('rooms.add')}
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
            <TextField
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>{t('rooms.type')}</InputLabel>
              <Select
                value={typeFilter}
                onChange={handleTypeChange}
                label={t('rooms.type')}
              >
                <MenuItem value="">
                  <em>{t('common.all')}</em>
                </MenuItem>
                {roomTypes.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {i18n.language === 'si' ? type.labelSi : type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>{t('rooms.building')}</InputLabel>
              <Select
                value={buildingFilter}
                onChange={handleBuildingChange}
                label={t('rooms.building')}
              >
                <MenuItem value="">
                  <em>{t('common.all')}</em>
                </MenuItem>
                {buildings.map((building) => (
                  <MenuItem key={building} value={building}>
                    {building}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Rooms Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : rooms.length === 0 ? (
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <Room sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {searchTerm || typeFilter || buildingFilter ? t('common.noResults') : t('rooms.noRooms')}
              </Typography>
              <Typography color="text.secondary">
                {searchTerm || typeFilter || buildingFilter ? t('common.tryDifferentSearch') : t('rooms.addFirstRoom')}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('rooms.name')}</TableCell>
                      <TableCell>{t('rooms.number')}</TableCell>
                      <TableCell>{t('rooms.type')}</TableCell>
                      <TableCell>{t('rooms.building')}</TableCell>
                      <TableCell>{t('rooms.floor')}</TableCell>
                      <TableCell>{t('rooms.capacity')}</TableCell>
                      <TableCell>{t('rooms.assignedClass')}</TableCell>
                      <TableCell align="right">{t('common.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {rooms.map((room) => (
                      <TableRow key={room._id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar sx={{ bgcolor: 'info.main' }}>
                              {getRoomInitials(room)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {getRoomName(room)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {room.isActive ? t('common.active') : t('common.inactive')}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={room.number}
                            size="small"
                            variant="outlined"
                            sx={{ fontFamily: 'monospace' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {getRoomTypeLabel(room.type)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Business fontSize="small" color="action" />
                            <Typography variant="body2">
                              {room.building}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Layers fontSize="small" color="action" />
                            <Typography variant="body2">
                              {room.floor}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <People fontSize="small" color="action" />
                            <Typography variant="body2">
                              {room.capacity}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Class fontSize="small" color="action" />
                            <Typography variant="body2">
                              {getAssignedClassName(room.assignedClass)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" gap={1}>
                            <Tooltip title={t('common.view')}>
                              <IconButton
                                size="small"
                                onClick={() => handleViewRoom(room)}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.edit')}>
                              <IconButton
                                size="small"
                                onClick={() => handleEditRoom(room)}
                              >
                                <Edit />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.delete')}>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteRoom(room._id)}
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Room Dialog - Placeholder for now */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' && t('rooms.add')}
          {dialogMode === 'edit' && t('rooms.edit')}
          {dialogMode === 'view' && t('common.view')}
        </DialogTitle>
        <DialogContent>
          <Box py={2}>
            <Typography>
              {dialogMode === 'add' && 'Add room form will be implemented here'}
              {dialogMode === 'edit' && 'Edit room form will be implemented here'}
              {dialogMode === 'view' && 'Room details view will be implemented here'}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            {t('common.close')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default RoomList
