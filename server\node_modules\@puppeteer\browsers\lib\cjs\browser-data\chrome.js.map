{"version": 3, "file": "chrome.js", "sourceRoot": "", "sources": ["../../../src/browser-data/chrome.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAEH,gDAAwB;AAExB,gDAAuC;AAEvC,yCAAiE;AAEjE,SAAS,MAAM,CAAC,QAAyB;IACvC,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,SAAS,CAAC;QACnB,KAAK,0BAAe,CAAC,OAAO;YAC1B,OAAO,WAAW,CAAC;QACrB,KAAK,0BAAe,CAAC,GAAG;YACtB,OAAO,SAAS,CAAC;QACnB,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,OAAO,CAAC;QACjB,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,OAAO,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAgB,kBAAkB,CAChC,QAAyB,EACzB,OAAe,EACf,OAAO,GAAG,6DAA6D;IAEvE,OAAO,GAAG,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1E,CAAC;AAND,gDAMC;AAED,SAAgB,mBAAmB,CACjC,QAAyB,EACzB,OAAe;IAEf,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,UAAU,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACvE,CAAC;AALD,kDAKC;AAED,SAAgB,sBAAsB,CACpC,QAAyB,EACzB,QAAgB;IAEhB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,0BAAe,CAAC,GAAG,CAAC;QACzB,KAAK,0BAAe,CAAC,OAAO;YAC1B,OAAO,cAAI,CAAC,IAAI,CACd,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,EAC5B,+BAA+B,EAC/B,UAAU,EACV,OAAO,EACP,2BAA2B,CAC5B,CAAC;QACJ,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,cAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;QAC/C,KAAK,0BAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,cAAI,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,YAAY,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AApBD,wDAoBC;AAEM,KAAK,UAAU,iCAAiC,CACrD,OAA6B;IAE7B,MAAM,IAAI,GAAG,CAAC,MAAM,IAAA,qBAAO,EACzB,IAAI,GAAG,CACL,qFAAqF,CACtF,CACF,CAEA,CAAC;IAEF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAE,CAAC;QAC/D,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,OACE,IAKD,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtB,CAAC;AAvBD,8EAuBC;AAEM,KAAK,UAAU,mCAAmC,CACvD,SAAiB;IAEjB,MAAM,IAAI,GAAG,CAAC,MAAM,IAAA,qBAAO,EACzB,IAAI,GAAG,CACL,0FAA0F,CAC3F,CACF,CAEA,CAAC;IACF,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAEnB,CAAC;AAChB,CAAC;AAbD,kFAaC;AAEM,KAAK,UAAU,+BAA+B;AACnD;;GAEG;AACH,WAAmB;IAEnB,MAAM,IAAI,GAAG,CAAC,MAAM,IAAA,qBAAO,EACzB,IAAI,GAAG,CACL,4FAA4F,CAC7F,CACF,CAEA,CAAC;IACF,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAEjB,CAAC;AAChB,CAAC;AAhBD,0EAgBC;AAQM,KAAK,UAAU,cAAc,CAClC,OAAsC;IAEtC,IACE,MAAM,CAAC,MAAM,CAAC,+BAAoB,CAAC,CAAC,QAAQ,CAC1C,OAA+B,CAChC,EACD,CAAC;QACD,OAAO,CACL,MAAM,iCAAiC,CAAC,OAA+B,CAAC,CACzE,CAAC,OAAO,CAAC;IACZ,CAAC;IACD,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3B,2BAA2B;QAC3B,OAAO,CAAC,MAAM,mCAAmC,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;IACvE,CAAC;IACD,IAAI,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACrC,wDAAwD;QACxD,OAAO,CAAC,MAAM,+BAA+B,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;IACnE,CAAC;IACD,OAAO;AACT,CAAC;AArBD,wCAqBC;AAED,SAAgB,2BAA2B,CACzC,QAAyB,EACzB,OAA6B;IAE7B,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,0BAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,0BAAe,CAAC,KAAK;YACxB,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,+BAAoB,CAAC,MAAM;oBAC9B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,2CAA2C,CAAC;gBACnF,KAAK,+BAAoB,CAAC,IAAI;oBAC5B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,gDAAgD,CAAC;gBACxF,KAAK,+BAAoB,CAAC,MAAM;oBAC9B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,+CAA+C,CAAC;gBACvF,KAAK,+BAAoB,CAAC,GAAG;oBAC3B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,+CAA+C,CAAC;YACzF,CAAC;QACH,KAAK,0BAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,0BAAe,CAAC,GAAG;YACtB,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,+BAAoB,CAAC,MAAM;oBAC9B,OAAO,8DAA8D,CAAC;gBACxE,KAAK,+BAAoB,CAAC,IAAI;oBAC5B,OAAO,wEAAwE,CAAC;gBAClF,KAAK,+BAAoB,CAAC,MAAM;oBAC9B,OAAO,4EAA4E,CAAC;gBACtF,KAAK,+BAAoB,CAAC,GAAG;oBAC3B,OAAO,sEAAsE,CAAC;YAClF,CAAC;QACH,KAAK,0BAAe,CAAC,KAAK;YACxB,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,+BAAoB,CAAC,MAAM;oBAC9B,OAAO,2BAA2B,CAAC;gBACrC,KAAK,+BAAoB,CAAC,IAAI;oBAC5B,OAAO,gCAAgC,CAAC;gBAC1C,KAAK,+BAAoB,CAAC,GAAG;oBAC3B,OAAO,oCAAoC,CAAC;YAChD,CAAC;IACL,CAAC;IAED,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,QAAQ,QAAQ,GAAG,CAC5E,CAAC;AACJ,CAAC;AA3CD,kEA2CC"}