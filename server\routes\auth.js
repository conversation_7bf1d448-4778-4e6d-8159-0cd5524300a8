const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { User, Teacher } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '7d'
  });
};

// Register new user
router.post('/register', [
  body('username').isLength({ min: 3 }).trim().escape(),
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  body('role').isIn(['admin', 'teacher', 'student', 'parent']),
  body('profile.firstName').notEmpty().trim().escape(),
  body('profile.lastName').notEmpty().trim().escape()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const { username, email, password, role, profile } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        message: 'User already exists with this email or username',
        messageSinhala: 'මෙම ඊමේල් හෝ පරිශීලක නාමය සමඟ පරිශීලකයෙකු දැනටමත් පවතී'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      role,
      profile
    });

    await user.save();

    // If role is teacher, create teacher profile
    if (role === 'teacher') {
      const teacher = new Teacher({
        user: user._id,
        employeeId: `T${Date.now()}` // Generate temporary employee ID
      });
      await teacher.save();
    }

    const token = generateToken(user._id);

    res.status(201).json({
      message: 'User registered successfully',
      messageSinhala: 'පරිශීලකයා සාර්ථකව ලියාපදිංචි විය',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        profile: user.profile
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      message: 'Registration failed',
      messageSinhala: 'ලියාපදිංචිය අසාර්ථක විය',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Login user
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        message: 'Invalid email or password',
        messageSinhala: 'වලංගු නොවන ඊමේල් හෝ මුරපදය'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        message: 'Account is deactivated',
        messageSinhala: 'ගිණුම අක්‍රිය කර ඇත'
      });
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        message: 'Invalid email or password',
        messageSinhala: 'වලංගු නොවන ඊමේල් හෝ මුරපදය'
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    const token = generateToken(user._id);

    res.json({
      message: 'Login successful',
      messageSinhala: 'පිවිසීම සාර්ථකයි',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        profile: user.profile,
        preferences: user.preferences
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      message: 'Login failed',
      messageSinhala: 'පිවිසීම අසාර්ථක විය',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');
    
    let additionalData = {};
    if (user.role === 'teacher') {
      const teacher = await Teacher.findOne({ user: user._id })
        .populate('subjects', 'name nameSinhala code')
        .populate('classes', 'name nameSinhala grade section');
      additionalData.teacher = teacher;
    }

    res.json({
      message: 'Profile retrieved successfully',
      messageSinhala: 'පැතිකඩ සාර්ථකව ලබා ගන්නා ලදී',
      user,
      ...additionalData
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      message: 'Failed to fetch profile',
      messageSinhala: 'පැතිකඩ ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Update user preferences
router.patch('/preferences', authenticateToken, [
  body('language').optional().isIn(['si', 'en']),
  body('theme').optional().isIn(['light', 'dark'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const updates = {};
    if (req.body.language) updates['preferences.language'] = req.body.language;
    if (req.body.theme) updates['preferences.theme'] = req.body.theme;
    if (req.body.notifications) {
      if (req.body.notifications.email !== undefined) {
        updates['preferences.notifications.email'] = req.body.notifications.email;
      }
      if (req.body.notifications.push !== undefined) {
        updates['preferences.notifications.push'] = req.body.notifications.push;
      }
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { $set: updates },
      { new: true, select: '-password' }
    );

    res.json({
      message: 'Preferences updated successfully',
      messageSinhala: 'මනාපයන් සාර්ථකව යාවත්කාලීන කරන ලදී',
      user
    });
  } catch (error) {
    console.error('Preferences update error:', error);
    res.status(500).json({
      message: 'Failed to update preferences',
      messageSinhala: 'මනාපයන් යාවත්කාලීන කිරීමට අසමත් විය'
    });
  }
});

// Get all users (admin only)
router.get('/users', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied. Admin only.',
        messageSinhala: 'ප්‍රවේශය ප්‍රතික්ෂේප විය. පරිපාලක පමණි.'
      });
    }

    const { page = 1, limit = 50, role, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (role && role !== 'all') {
      query.role = role;
    }
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { 'profile.firstName': { $regex: search, $options: 'i' } },
        { 'profile.lastName': { $regex: search, $options: 'i' } }
      ];
    }

    const users = await User.find(query)
      .select('-password')
      .populate('profile')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(query);

    res.json({
      message: 'Users retrieved successfully',
      messageSinhala: 'පරිශීලකයින් සාර්ථකව ලබා ගන්නා ලදී',
      users,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalUsers: total,
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Users fetch error:', error);
    res.status(500).json({
      message: 'Server error',
      messageSinhala: 'සේවාදායක දෝෂයක්'
    });
  }
});

// Logout (client-side token removal, but we can log it)
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just acknowledge the logout
    res.json({
      message: 'Logged out successfully',
      messageSinhala: 'සාර්ථකව ඉවත් විය'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      message: 'Logout failed',
      messageSinhala: 'ඉවත්වීම අසාර්ථක විය'
    });
  }
});

module.exports = router;
