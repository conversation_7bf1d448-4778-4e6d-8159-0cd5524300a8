import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  FormControlLabel,
  Switch,
  Alert,
  Autocomplete,
  Chip
} from '@mui/material'
import axios from 'axios'

const SubjectForm = ({ open, onClose, subject, mode, onSuccess }) => {
  const { t, i18n } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [subjects, setSubjects] = useState([])

  const [formData, setFormData] = useState({
    name: '',
    nameSinhala: '',
    code: '',
    department: '',
    grade: 1,
    periodsPerWeek: 1,
    duration: 40,
    isCore: true,
    color: '#1976d2',
    prerequisites: [],
    description: ''
  })

  const departments = [
    { value: 'mathematics', label: 'Mathematics', labelSi: 'ගණිතය' },
    { value: 'science', label: 'Science', labelSi: 'විද්‍යාව' },
    { value: 'languages', label: 'Languages', labelSi: 'භාෂා' },
    { value: 'social_studies', label: 'Social Studies', labelSi: 'සමාජ අධ්‍යයනය' },
    { value: 'arts', label: 'Arts', labelSi: 'කලා' },
    { value: 'physical_education', label: 'Physical Education', labelSi: 'ශාරීරික අධ්‍යාපනය' },
    { value: 'technology', label: 'Technology', labelSi: 'තාක්ෂණය' },
    { value: 'religion', label: 'Religion', labelSi: 'ආගම' }
  ]

  useEffect(() => {
    if (open) {
      fetchSubjects()
      if (subject && mode !== 'add') {
        setFormData({
          name: subject.name || '',
          nameSinhala: subject.nameSinhala || '',
          code: subject.code || '',
          department: subject.department || '',
          grade: subject.grade || 1,
          periodsPerWeek: subject.periodsPerWeek || 1,
          duration: subject.duration || 40,
          isCore: subject.isCore !== undefined ? subject.isCore : true,
          color: subject.color || '#1976d2',
          prerequisites: subject.prerequisites?.map(p => p._id) || [],
          description: subject.description || ''
        })
      } else {
        setFormData({
          name: '',
          nameSinhala: '',
          code: '',
          department: '',
          grade: 1,
          periodsPerWeek: 1,
          duration: 40,
          isCore: true,
          color: '#1976d2',
          prerequisites: [],
          description: ''
        })
      }
    }
  }, [open, subject, mode])

  const fetchSubjects = async () => {
    try {
      const response = await axios.get('/api/subjects')
      setSubjects(response.data.subjects || [])
    } catch (err) {
      console.error('Failed to fetch subjects:', err)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!formData.name || !formData.nameSinhala || !formData.code || !formData.department) {
        setError('Please fill in all required fields')
        return
      }

      const url = mode === 'add' ? '/api/subjects' : `/api/subjects/${subject._id}`
      const method = mode === 'add' ? 'post' : 'put'

      await axios[method](url, formData)
      
      onSuccess?.()
      onClose()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save subject')
    } finally {
      setLoading(false)
    }
  }

  const getSubjectName = (subject) => {
    return i18n.language === 'si' ? subject.nameSinhala : subject.name
  }

  const getDepartmentLabel = (department) => {
    const dept = departments.find(d => d.value === department)
    return dept ? (i18n.language === 'si' ? dept.labelSi : dept.label) : department
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'add' && t('subjects.add')}
        {mode === 'edit' && t('subjects.edit')}
        {mode === 'view' && t('common.view')}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={`${t('subjects.name')} (English)`}
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={`${t('subjects.name')} (Sinhala)`}
                value={formData.nameSinhala}
                onChange={(e) => handleChange('nameSinhala', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                label={t('subjects.code')}
                value={formData.code}
                onChange={(e) => handleChange('code', e.target.value.toUpperCase())}
                disabled={mode === 'view'}
                placeholder="MATH01, SCI02, etc."
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>{t('subjects.department')}</InputLabel>
                <Select
                  value={formData.department}
                  onChange={(e) => handleChange('department', e.target.value)}
                  label={t('subjects.department')}
                  disabled={mode === 'view'}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                        width: 250,
                      },
                    },
                  }}
                >
                  {departments.map((dept) => (
                    <MenuItem key={dept.value} value={dept.value}>
                      {i18n.language === 'si' ? dept.labelSi : dept.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>{t('subjects.grade')}</InputLabel>
                <Select
                  value={formData.grade}
                  onChange={(e) => handleChange('grade', e.target.value)}
                  label={t('subjects.grade')}
                  disabled={mode === 'view'}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                        width: 200,
                      },
                    },
                  }}
                >
                  {Array.from({ length: 13 }, (_, i) => i + 1).map((grade) => (
                    <MenuItem key={grade} value={grade}>
                      Grade {grade}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label={t('subjects.periodsPerWeek')}
                value={formData.periodsPerWeek}
                onChange={(e) => handleChange('periodsPerWeek', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 1, max: 10 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label={`${t('subjects.duration')} (minutes)`}
                value={formData.duration}
                onChange={(e) => handleChange('duration', parseInt(e.target.value))}
                disabled={mode === 'view'}
                inputProps={{ min: 30, max: 80 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="color"
                label="Subject Color"
                value={formData.color}
                onChange={(e) => handleChange('color', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isCore}
                    onChange={(e) => handleChange('isCore', e.target.checked)}
                    disabled={mode === 'view'}
                  />
                }
                label={formData.isCore ? t('subjects.core') : t('subjects.optional')}
              />
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={subjects.filter(s => s._id !== subject?._id)}
                getOptionLabel={(option) => getSubjectName(option)}
                value={subjects.filter(s => formData.prerequisites.includes(s._id))}
                onChange={(event, newValue) => {
                  handleChange('prerequisites', newValue.map(v => v._id))
                }}
                disabled={mode === 'view'}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={getSubjectName(option)}
                      {...getTagProps({ index })}
                      key={option._id}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('subjects.prerequisites')}
                    placeholder="Select prerequisite subjects"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                disabled={mode === 'view'}
                placeholder="Brief description of the subject..."
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          {t('common.cancel')}
        </Button>
        {mode !== 'view' && (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : t('common.save')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default SubjectForm
