import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Add,
  SwapHoriz,
  Edit,
  Delete,
  Search,
  Visibility,
  Person,
  Class,
  Schedule,
  CheckCircle,
  Cancel,
  Pending
} from '@mui/icons-material'
import axios from 'axios'
import SubstitutionForm from './SubstitutionForm'

const SubstitutionList = () => {
  const { t, i18n } = useTranslation()
  const [substitutions, setSubstitutions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedSubstitution, setSelectedSubstitution] = useState(null)
  const [dialogMode, setDialogMode] = useState('add') // 'add', 'edit', 'view'

  const statusOptions = [
    { value: 'pending', label: 'Pending', labelSi: 'අපේක්ෂිත', color: 'warning', icon: <Pending /> },
    { value: 'confirmed', label: 'Confirmed', labelSi: 'තහවුරු කළ', color: 'info', icon: <CheckCircle /> },
    { value: 'declined', label: 'Declined', labelSi: 'ප්‍රතික්ෂේප කළ', color: 'error', icon: <Cancel /> },
    { value: 'completed', label: 'Completed', labelSi: 'සම්පූර්ණ', color: 'success', icon: <CheckCircle /> }
  ]

  const typeOptions = [
    { value: 'teacher_substitution', label: 'Teacher Substitution', labelSi: 'ගුරු ආදේශනය' },
    { value: 'free_period', label: 'Free Period', labelSi: 'නිදහස් කාලය' },
    { value: 'combined_class', label: 'Combined Class', labelSi: 'ඒකාබද්ධ පන්තිය' },
    { value: 'self_study', label: 'Self Study', labelSi: 'ස්වයං අධ්‍යයනය' },
    { value: 'cancelled', label: 'Cancelled', labelSi: 'අවලංගු කළ' }
  ]

  useEffect(() => {
    fetchSubstitutions()
  }, [page, searchTerm, statusFilter, typeFilter])

  const fetchSubstitutions = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/substitutions', {
        params: {
          page,
          limit: 10,
          search: searchTerm,
          status: statusFilter,
          type: typeFilter
        }
      })
      setSubstitutions(response.data.substitutions)
      setTotalPages(response.data.pagination.totalPages)
      setError(null)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch substitutions')
    } finally {
      setLoading(false)
    }
  }

  const handleAddSubstitution = () => {
    setSelectedSubstitution(null)
    setDialogMode('add')
    setOpenDialog(true)
  }

  const handleEditSubstitution = (substitution) => {
    setSelectedSubstitution(substitution)
    setDialogMode('edit')
    setOpenDialog(true)
  }

  const handleViewSubstitution = (substitution) => {
    setSelectedSubstitution(substitution)
    setDialogMode('view')
    setOpenDialog(true)
  }

  const handleDeleteSubstitution = async (substitutionId) => {
    if (window.confirm(t('messages.confirmDelete'))) {
      try {
        await axios.delete(`/api/substitutions/${substitutionId}`)
        fetchSubstitutions()
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete substitution')
      }
    }
  }

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  const handleStatusChange = (event) => {
    setStatusFilter(event.target.value)
    setPage(1)
  }

  const handleTypeChange = (event) => {
    setTypeFilter(event.target.value)
    setPage(1)
  }

  const handlePageChange = (event, newPage) => {
    setPage(newPage)
  }

  const getStatusOption = (status) => {
    return statusOptions.find(s => s.value === status)
  }

  const getTypeLabel = (type) => {
    const typeOption = typeOptions.find(t => t.value === type)
    return typeOption ? (i18n.language === 'si' ? typeOption.labelSi : typeOption.label) : type
  }

  const getTeacherName = (teacher) => {
    if (!teacher?.user?.profile) return 'Unknown'
    const firstName = teacher.user.profile.firstName || ''
    const lastName = teacher.user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim() || 'Unknown'
  }

  const getClassName = (classItem) => {
    if (!classItem) return 'Unknown'
    return i18n.language === 'si' ? classItem.nameSinhala : classItem.name
  }

  const getSubjectName = (subject) => {
    if (!subject) return 'Unknown'
    return i18n.language === 'si' ? subject.nameSinhala : subject.name
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3} sx={{ gap: 2 }}>
        <Typography variant="h4">{t('substitutions.title')}</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={handleAddSubstitution}>
          {t('substitutions.add')}
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
            <TextField
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>{t('substitutions.status')}</InputLabel>
              <Select
                value={statusFilter}
                onChange={handleStatusChange}
                label={t('substitutions.status')}
              >
                <MenuItem value="">
                  <em>{t('common.all')}</em>
                </MenuItem>
                {statusOptions.map((status) => (
                  <MenuItem key={status.value} value={status.value}>
                    {i18n.language === 'si' ? status.labelSi : status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>{t('common.type')}</InputLabel>
              <Select
                value={typeFilter}
                onChange={handleTypeChange}
                label={t('common.type')}
              >
                <MenuItem value="">
                  <em>{t('common.all')}</em>
                </MenuItem>
                {typeOptions.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {i18n.language === 'si' ? type.labelSi : type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Substitutions Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : substitutions.length === 0 ? (
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <SwapHoriz sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {searchTerm || statusFilter || typeFilter ? t('common.noResults') : t('substitutions.noSubstitutions')}
              </Typography>
              <Typography color="text.secondary">
                {searchTerm || statusFilter || typeFilter ? t('common.tryDifferentSearch') : t('substitutions.addFirstSubstitution')}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('substitutions.date')}</TableCell>
                      <TableCell>{t('substitutions.period')}</TableCell>
                      <TableCell>{t('substitutions.originalTeacher')}</TableCell>
                      <TableCell>{t('substitutions.substituteTeacher')}</TableCell>
                      <TableCell>{t('substitutions.subject')}</TableCell>
                      <TableCell>{t('substitutions.class')}</TableCell>
                      <TableCell>{t('substitutions.status')}</TableCell>
                      <TableCell align="right">{t('common.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {substitutions.map((substitution) => {
                      const statusOption = getStatusOption(substitution.status)
                      return (
                        <TableRow key={substitution._id} hover>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDate(substitution.date)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={`Period ${substitution.period}`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Person fontSize="small" color="action" />
                              <Typography variant="body2">
                                {getTeacherName(substitution.originalTeacher)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Person fontSize="small" color="action" />
                              <Typography variant="body2">
                                {substitution.substituteTeacher ?
                                  getTeacherName(substitution.substituteTeacher) :
                                  'Not assigned'
                                }
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {getSubjectName(substitution.subject)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Class fontSize="small" color="action" />
                              <Typography variant="body2">
                                {getClassName(substitution.class)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={i18n.language === 'si' ? statusOption?.labelSi : statusOption?.label}
                              color={statusOption?.color}
                              size="small"
                              icon={statusOption?.icon}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Box display="flex" gap={1}>
                              <Tooltip title={t('common.view')}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleViewSubstitution(substitution)}
                                >
                                  <Visibility />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title={t('common.edit')}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleEditSubstitution(substitution)}
                                >
                                  <Edit />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title={t('common.delete')}>
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDeleteSubstitution(substitution._id)}
                                >
                                  <Delete />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Substitution Form Dialog */}
      <SubstitutionForm
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        substitution={selectedSubstitution}
        mode={dialogMode}
        onSuccess={fetchSubstitutions}
      />
    </Box>
  )
}

export default SubstitutionList
