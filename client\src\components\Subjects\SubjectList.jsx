import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Add,
  Subject,
  Edit,
  Delete,
  Search,
  Visibility,
  School,
  Code,
  Schedule
} from '@mui/icons-material'
import axios from 'axios'
import SubjectForm from './SubjectForm'

const SubjectList = () => {
  const { t, i18n } = useTranslation()
  const [subjects, setSubjects] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [departmentFilter, setDepartmentFilter] = useState('')
  const [gradeFilter, setGradeFilter] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedSubject, setSelectedSubject] = useState(null)
  const [dialogMode, setDialogMode] = useState('add') // 'add', 'edit', 'view'

  const departments = [
    { value: 'mathematics', label: 'Mathematics', labelSi: 'ගණිතය' },
    { value: 'science', label: 'Science', labelSi: 'විද්‍යාව' },
    { value: 'languages', label: 'Languages', labelSi: 'භාෂා' },
    { value: 'social_studies', label: 'Social Studies', labelSi: 'සමාජ අධ්‍යයනය' },
    { value: 'arts', label: 'Arts', labelSi: 'කලා' },
    { value: 'physical_education', label: 'Physical Education', labelSi: 'ශාරීරික අධ්‍යාපනය' },
    { value: 'technology', label: 'Technology', labelSi: 'තාක්ෂණය' },
    { value: 'religion', label: 'Religion', labelSi: 'ආගම' }
  ]

  useEffect(() => {
    fetchSubjects()
  }, [page, searchTerm, departmentFilter, gradeFilter])

  const fetchSubjects = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/subjects', {
        params: {
          page,
          limit: 10,
          search: searchTerm,
          department: departmentFilter,
          grade: gradeFilter
        }
      })
      setSubjects(response.data.subjects)
      setTotalPages(response.data.pagination.totalPages)
      setError(null)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch subjects')
    } finally {
      setLoading(false)
    }
  }

  const handleAddSubject = () => {
    setSelectedSubject(null)
    setDialogMode('add')
    setOpenDialog(true)
  }

  const handleEditSubject = (subject) => {
    setSelectedSubject(subject)
    setDialogMode('edit')
    setOpenDialog(true)
  }

  const handleViewSubject = (subject) => {
    setSelectedSubject(subject)
    setDialogMode('view')
    setOpenDialog(true)
  }

  const handleDeleteSubject = async (subjectId) => {
    if (window.confirm(t('messages.confirmDelete'))) {
      try {
        await axios.delete(`/api/subjects/${subjectId}`)
        fetchSubjects()
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete subject')
      }
    }
  }

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  const handleDepartmentChange = (event) => {
    setDepartmentFilter(event.target.value)
    setPage(1)
  }

  const handleGradeChange = (event) => {
    setGradeFilter(event.target.value)
    setPage(1)
  }

  const handlePageChange = (event, newPage) => {
    setPage(newPage)
  }

  const getSubjectInitials = (subject) => {
    return subject.code || subject.name.substring(0, 2).toUpperCase()
  }

  const getSubjectName = (subject) => {
    return i18n.language === 'si' ? subject.nameSinhala : subject.name
  }

  const getDepartmentLabel = (department) => {
    const dept = departments.find(d => d.value === department)
    return dept ? (i18n.language === 'si' ? dept.labelSi : dept.label) : department
  }

  const getSubjectColor = (subject) => {
    return subject.color || '#1976d2'
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3} sx={{ gap: 2 }}>
        <Typography variant="h4">{t('subjects.title')}</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={handleAddSubject}>
          {t('subjects.add')}
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
            <TextField
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>{t('subjects.department')}</InputLabel>
              <Select
                value={departmentFilter}
                onChange={handleDepartmentChange}
                label={t('subjects.department')}
              >
                <MenuItem value="">
                  <em>{t('common.all')}</em>
                </MenuItem>
                {departments.map((dept) => (
                  <MenuItem key={dept.value} value={dept.value}>
                    {i18n.language === 'si' ? dept.labelSi : dept.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>{t('subjects.grade')}</InputLabel>
              <Select
                value={gradeFilter}
                onChange={handleGradeChange}
                label={t('subjects.grade')}
              >
                <MenuItem value="">
                  <em>{t('common.all')}</em>
                </MenuItem>
                {Array.from({ length: 13 }, (_, i) => i + 1).map((grade) => (
                  <MenuItem key={grade} value={grade}>
                    Grade {grade}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Subjects Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : subjects.length === 0 ? (
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <Subject sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {searchTerm || departmentFilter || gradeFilter ? t('common.noResults') : t('subjects.noSubjects')}
              </Typography>
              <Typography color="text.secondary">
                {searchTerm || departmentFilter || gradeFilter ? t('common.tryDifferentSearch') : t('subjects.addFirstSubject')}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('subjects.name')}</TableCell>
                      <TableCell>{t('subjects.code')}</TableCell>
                      <TableCell>{t('subjects.department')}</TableCell>
                      <TableCell>{t('subjects.grade')}</TableCell>
                      <TableCell>{t('subjects.periodsPerWeek')}</TableCell>
                      <TableCell>{t('subjects.duration')}</TableCell>
                      <TableCell align="right">{t('common.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {subjects.map((subject) => (
                      <TableRow key={subject._id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar
                              sx={{
                                bgcolor: getSubjectColor(subject),
                                width: 40,
                                height: 40
                              }}
                            >
                              {getSubjectInitials(subject)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {getSubjectName(subject)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {subject.isCore ? t('subjects.core') : t('subjects.optional')}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={subject.code}
                            size="small"
                            variant="outlined"
                            sx={{ fontFamily: 'monospace' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {getDepartmentLabel(subject.department)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`Grade ${subject.grade}`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Schedule fontSize="small" color="action" />
                            <Typography variant="body2">
                              {subject.periodsPerWeek}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {subject.duration} min
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" gap={1}>
                            <Tooltip title={t('common.view')}>
                              <IconButton
                                size="small"
                                onClick={() => handleViewSubject(subject)}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.edit')}>
                              <IconButton
                                size="small"
                                onClick={() => handleEditSubject(subject)}
                              >
                                <Edit />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.delete')}>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteSubject(subject._id)}
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Subject Form Dialog */}
      <SubjectForm
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        subject={selectedSubject}
        mode={dialogMode}
        onSuccess={fetchSubjects}
      />
    </Box>
  )
}

export default SubjectList
