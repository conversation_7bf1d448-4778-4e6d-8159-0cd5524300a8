import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar
} from '@mui/material'
import {
  Add,
  People,
  Edit,
  Delete,
  Search,
  Visibility,
  School,
  Person
} from '@mui/icons-material'
import axios from 'axios'
import TeacherForm from './TeacherForm'

const TeacherList = () => {
  const { t, i18n } = useTranslation()
  const [teachers, setTeachers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedTeacher, setSelectedTeacher] = useState(null)
  const [dialogMode, setDialogMode] = useState('add') // 'add', 'edit', 'view'

  useEffect(() => {
    fetchTeachers()
  }, [page, searchTerm])

  const fetchTeachers = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/teachers', {
        params: {
          page,
          limit: 10,
          search: searchTerm
        }
      })
      setTeachers(response.data.teachers)
      setTotalPages(response.data.pagination.totalPages)
      setError(null)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch teachers')
    } finally {
      setLoading(false)
    }
  }

  const handleAddTeacher = () => {
    setSelectedTeacher(null)
    setDialogMode('add')
    setOpenDialog(true)
  }

  const handleEditTeacher = (teacher) => {
    setSelectedTeacher(teacher)
    setDialogMode('edit')
    setOpenDialog(true)
  }

  const handleViewTeacher = (teacher) => {
    setSelectedTeacher(teacher)
    setDialogMode('view')
    setOpenDialog(true)
  }

  const handleDeleteTeacher = async (teacherId) => {
    if (window.confirm(t('messages.confirmDelete'))) {
      try {
        await axios.delete(`/api/teachers/${teacherId}`)
        fetchTeachers()
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete teacher')
      }
    }
  }

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  const handlePageChange = (event, newPage) => {
    setPage(newPage)
  }

  const getInitials = (user) => {
    if (!user?.profile) return 'T'
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const getFullName = (user) => {
    if (!user?.profile) return 'Unknown'
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim() || 'Unknown'
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3} sx={{ gap: 2 }}>
        <Typography variant="h4">{t('teachers.title')}</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={handleAddTeacher}>
          {t('teachers.add')}
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center">
            <TextField
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Teachers Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : teachers.length === 0 ? (
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <People sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {searchTerm ? t('common.noResults') : t('teachers.noTeachers')}
              </Typography>
              <Typography color="text.secondary">
                {searchTerm ? t('common.tryDifferentSearch') : t('teachers.addFirstTeacher')}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('teachers.name')}</TableCell>
                      <TableCell>{t('teachers.employeeId')}</TableCell>
                      <TableCell>{t('teachers.subjects')}</TableCell>
                      <TableCell>{t('teachers.classes')}</TableCell>
                      <TableCell>{t('common.status')}</TableCell>
                      <TableCell align="right">{t('common.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {teachers.map((teacher) => (
                      <TableRow key={teacher._id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              {getInitials(teacher.user)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {getFullName(teacher.user)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {teacher.user?.email}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {teacher.employeeId}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" gap={0.5} flexWrap="wrap">
                            {teacher.subjects?.slice(0, 2).map((subject) => (
                              <Chip
                                key={subject._id}
                                label={i18n.language === 'si' ? subject.nameSinhala : subject.name}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {teacher.subjects?.length > 2 && (
                              <Chip
                                label={`+${teacher.subjects.length - 2}`}
                                size="small"
                                variant="outlined"
                                color="primary"
                              />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {teacher.classes?.length || 0} {t('classes.title').toLowerCase()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={teacher.isActive ? t('teachers.active') : t('teachers.inactive')}
                            color={teacher.isActive ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" gap={1}>
                            <Tooltip title={t('common.view')}>
                              <IconButton
                                size="small"
                                onClick={() => handleViewTeacher(teacher)}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.edit')}>
                              <IconButton
                                size="small"
                                onClick={() => handleEditTeacher(teacher)}
                              >
                                <Edit />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.delete')}>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteTeacher(teacher._id)}
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Teacher Form Dialog */}
      <TeacherForm
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        teacher={selectedTeacher}
        mode={dialogMode}
        onSuccess={fetchTeachers}
      />
    </Box>
  )
}

export default TeacherList
